# Creating Custom Drawn Controls: Game-Style Button Tutorial

🎮 **Learn how to create your own custom drawn controls in DrawnUI**

In this advanced tutorial, we'll build a complete custom control from scratch. We'll use a game-style button as our example to demonstrate the fundamental concepts of creating reusable, high-performance drawn controls with bindable properties, visual effects, and interactive animations.

## 🎯 What We'll Learn

By the end of this tutorial, you'll understand:
- **🏗️ Custom Control Architecture** - How to extend DrawnUI base classes
- **🔗 Bindable Properties** - Creating properties that work with data binding
- **✨ Visual Effects** - Implementing bevel effects, gradients, and animations
- **🎮 Interactive Feedback** - Handling touch gestures with visual responses
- **⚡ Performance Optimization** - Using caching and efficient rendering
- **🎨 Dynamic Content** - Supporting optional images and customizable appearance

<img src="../images/custombutton.jpg" alt="Custom Button Tutorial" width="350" style="margin-top: 16px;" />

## 🏗️ Understanding Custom Control Architecture

### The Foundation: Extending SkiaLayout

Our custom button inherits from `SkiaLayout`, which provides the foundation for creating complex drawn controls:

```csharp
public class GameButton : SkiaLayout
{
    public GameButton()
    {
        UseCache = SkiaCacheType.Image; // Enable caching for performance
    }
}
```

### The CreateDefaultContent Pattern

DrawnUI uses the `CreateDefaultContent()` method to build the control's default content if defined. This will be called once only after the control is first time rendering. You could also build your fixed content in constructor, but we will use an overridable method, so you could subclass and customize your control content creation is needed.

```csharp
protected override void CreateDefaultContent()
{
    base.CreateDefaultContent();

    if (Views.Count == 0)
    {
        AddSubView(CreateView()); // Build our button structure
    }
}
```

## 🔗 Building Bindable Properties

### Creating Properties That Work With Data Binding

Custom controls need bindable properties to integrate seamlessly with XAML and data binding. Here's how to create them properly:

```csharp
public static readonly BindableProperty TextProperty = BindableProperty.Create(
    nameof(Text),
    typeof(string),
    typeof(GameButton),
    string.Empty);

public string Text
{
    get { return (string)GetValue(TextProperty); }
    set { SetValue(TextProperty, value); }
}
```

### Property Change Handling

For properties that affect appearance, we need to respond to changes:

```csharp
public static readonly BindableProperty TintColorProperty = BindableProperty.Create(
    nameof(TintColor),
    typeof(Color),
    typeof(GameButton),
    Colors.HotPink,
    propertyChanged: OnLookChanged); // Callback when property changes

private static void OnLookChanged(BindableObject bindable, object oldValue, object newValue)
{
    if (bindable is GameButton control)
    {
        control.MapProperties(); // Update visual appearance
    }
}
```

## 🎨 Creating the Visual Structure

### Building the Button's Visual Hierarchy

The heart of our custom control is the `CreateView()` method, which builds the complete visual structure:

```csharp
protected virtual SkiaShape CreateView()
{
    var startColor = TintColor;
    var endColor = TintColor.MakeDarker(20);

    return new SkiaShape()
    {
        UseCache = SkiaCacheType.Image,
        CornerRadius = 8,
        MinimumWidthRequest = 120,
        BackgroundColor = Colors.Black,
        BevelType = BevelType.Bevel,
        Bevel = new SkiaBevel()
        {
            Depth = 2,
            LightColor = Colors.White,
            ShadowColor = Colors.DarkBlue,
            Opacity = 0.33f,
        },
        Children =
        {
            new SkiaLayout()
            {
                Type = LayoutType.Row,
                Margin = new Thickness(16, 8),
                HorizontalOptions = LayoutOptions.Center,
                VerticalOptions = LayoutOptions.Center,
                Spacing = 6,
                Children =
                {
                    // Optional left image (icon)
                    new SkiaMediaImage()
                    {
                        VerticalOptions = LayoutOptions.Center,
                        WidthRequest = 40,
                        Aspect = TransformAspect.AspectFit
                    }.ObserveProperty(this, nameof(LeftImageSource),
                        me =>
                        {
                            me.Source = this.LeftImageSource;
                            me.IsVisible = LeftImageSource != null;
                        }),

                    // Button text
                    new SkiaRichLabel()
                    {
                        Text = this.Text,
                        UseCache = SkiaCacheType.Operations,
                        HorizontalTextAlignment = DrawTextAlignment.Center,
                        VerticalOptions = LayoutOptions.Center,
                        FontSize = 16,
                        FontAttributes = FontAttributes.Bold,
                        TextColor = Colors.White,
                    }.Assign(out TextLabel)
                    .ObserveProperty(this, nameof(Text),
                        me =>
                        {
                            me.Text = this.Text;
                        }),
                }
            }
        },
        FillGradient = new SkiaGradient()
        {
            StartXRatio = 0,
            EndXRatio = 1,
            StartYRatio = 0,
            EndYRatio = 0.5f,
            Colors = new Color[] { startColor, endColor, }
        },
    }.WithGestures((me, args, b) =>
    {
        // Handle touch gestures
        if (args.Type == TouchActionResult.Tapped)
        {
            Clicked?.Invoke(this, EventArgs.Empty);
        }
        else if (args.Type == TouchActionResult.Down)
        {
            SetButtonPressed(me);
        }
        else if (args.Type == TouchActionResult.Up)
        {
            SetButtonReleased(me);
            return null;
        }

        return me;
    });
}
```

### Property Observation Pattern

Notice how we use the `ObserveProperty` method to dynamically update child controls when properties change:

```csharp
.ObserveProperty(this, nameof(Text), me => { me.Text = this.Text; })
```

This pattern creates a subscription that automatically updates the child control whenever the parent property changes.

## 🎮 Adding Interactive Feedback

### Visual Press Effects

To create a realistic button press effect, we implement methods that change the visual appearance:

```csharp
public static void SetButtonPressed(SkiaShape btn)
{
    btn.Children[0].TranslationX = 1.5;
    btn.Children[0].TranslationY = 1.5;
    btn.BevelType = BevelType.Emboss;
}

public static void SetButtonReleased(SkiaShape btn)
{
    btn.Children[0].TranslationX = 0;
    btn.Children[0].TranslationY = 0;
    btn.BevelType = BevelType.Bevel;
}
```

### Handling Property Changes

When visual properties like `TintColor` change, we need to update multiple elements:

```csharp
private void MapProperties()
{
    if (Control != null)
    {
        DarkColor = this.TintColor.MakeDarker(25);
        Control.Bevel.ShadowColor = DarkColor;
        Control.FillGradient.Colors = new Color[] { TintColor, DarkColor, };
    }
}
```

## 📱 Using the Custom Control in XAML

Once our custom control is created, we can use it in XAML just like any built-in control:

```xml
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
             xmlns:customButton="using:DrawnUI.Tutorials.CustomButton">

    <draw:Canvas BackgroundColor="DarkSlateBlue">
        <draw:SkiaScroll>
            <draw:SkiaStack Spacing="30" Padding="20">

                <!-- Basic button -->
                <customButton:GameButton
                    Text="PLAY GAME"
                    Clicked="ClickedPlay"
                    HorizontalOptions="Center" />

                <!-- Button with custom color and icon -->
                <customButton:GameButton
                    Text="YO !"
                    TintColor="CornflowerBlue"
                    LeftImageSource="Images\banana.gif"
                    Clicked="ClickedBlue"
                    HorizontalOptions="Center" />

            </draw:SkiaStack>
        </draw:SkiaScroll>
    </draw:Canvas>
</ContentPage>
```

## 💡 Performance Optimization Tips

1. **Use Appropriate Caching**: We set `UseCache = SkiaCacheType.Image` for the main control and shape
2. **Cache Text Operations**: For text elements, use `UseCache = SkiaCacheType.Operations`
3. **Minimize Property Changes**: Only update properties that need to change
4. **Batch Updates**: Group multiple property changes together when possible
5. **Optimize Event Handlers**: Keep gesture handlers lightweight and efficient

## 🚀 Advanced Customization Ideas

### Different Color Schemes

```csharp
// Blue theme
<customButton:GameButton Text="BLUE BUTTON" TintColor="CornflowerBlue" />

// Green theme
<customButton:GameButton Text="GREEN BUTTON" TintColor="Green" />

// Orange theme
<customButton:GameButton Text="ORANGE BUTTON" TintColor="Orange" />
```

### Adding Icons

```csharp
// Button with left icon
<customButton:GameButton
    Text="WITH ICON"
    TintColor="Purple"
    LeftImageSource="Images\icon.png" />
```

## 🎯 What You've Learned

- **Custom Control Architecture**: How to extend `SkiaLayout` and implement `CreateDefaultContent`
- **Bindable Properties**: Creating and handling property changes with `BindableProperty`
- **Visual Structure**: Building complex visual hierarchies with nested controls
- **Property Observation**: Using `ObserveProperty` to dynamically update child controls
- **Interactive Feedback**: Implementing touch gestures with visual responses
- **Performance Optimization**: Using caching and efficient rendering techniques

## 🚀 Next Steps

Now that you understand how to create custom drawn controls, try:
- **Creating your own controls** for other UI elements like cards, toggles, or sliders
- **Extending this button** with additional features like long-press actions or disabled states
- **Implementing animations** using DrawnUI's animation capabilities
- **Creating a control library** of reusable custom controls for your applications

This custom control approach can be used to create any UI element you can imagine, giving you complete control over appearance and behavior! 🎮
